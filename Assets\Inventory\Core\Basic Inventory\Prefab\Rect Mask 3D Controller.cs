using UnityEngine;

[ExecuteInEditMode] // Para funcionar também no editor
public class RectMask3DController : MonoBehaviour
{
    [Tooltip("O RectTransform que definirá a área da máscara.")]
    public RectTransform maskingRect;

    [Tooltip("O Renderer do objeto 3D que será mascarado. Deve usar um material com o shader RectMask3D.")]
    public Renderer targetRenderer;

    [<PERSON>lt<PERSON>("A distância de suavização (fade) nas bordas da máscara.")]
    [Range(0, 1)]
    public float fadeDistance = 0f;

    private MaterialPropertyBlock _propBlock;
    private static readonly int MaskMinID = Shader.PropertyToID("_MaskMin");
    private static readonly int MaskMaxID = Shader.PropertyToID("_MaskMax");
    private static readonly int FadeDistanceID = Shader.PropertyToID("_FadeDistance");
    private static readonly int MaskWorldToLocalID = Shader.PropertyToID("_MaskWorldToLocal");

    void LateUpdate()
    {
        if (maskingRect == null || targetRenderer == null)
        {
            return;
        }

        _propBlock ??= new MaterialPropertyBlock();

        // Nova abordagem: Usar coordenadas locais do RectTransform para masking orientado
        // Isso permite que a máscara funcione corretamente independente da rotação

        // Obter o tamanho do rect em coordenadas locais
        Rect localRect = maskingRect.rect;
        Vector3 minBounds = new(localRect.xMin, localRect.yMin, 0);
        Vector3 maxBounds = new(localRect.xMax, localRect.yMax, 0);

        // Calcular a matriz de transformação do mundo para o espaço local do RectTransform
        Matrix4x4 worldToLocal = maskingRect.worldToLocalMatrix;

        // Atualiza as propriedades do material via MaterialPropertyBlock (mais otimizado)
        targetRenderer.GetPropertyBlock(_propBlock);
        _propBlock.SetVector(MaskMinID, new Vector4(minBounds.x, minBounds.y, minBounds.z, 0));
        _propBlock.SetVector(MaskMaxID, new Vector4(maxBounds.x, maxBounds.y, maxBounds.z, 0));
        _propBlock.SetFloat(FadeDistanceID, fadeDistance);
        _propBlock.SetMatrix(MaskWorldToLocalID, worldToLocal);
        targetRenderer.SetPropertyBlock(_propBlock);
    }

    // Método para facilitar a configuração a partir de outros scripts
    public void SetTarget(Renderer renderer)
    {
        Debug.Log("SetTarget called");
        targetRenderer = renderer;
        // Cria uma instância do material para este objeto para não afetar outros
        // que usam o mesmo material base.
        if (targetRenderer != null)
        {
            targetRenderer.material = new Material(targetRenderer.material);
        }
    }
    
    [ContextMenu("Set Target")]
    public void SetTarget()
    {
        if (targetRenderer != null)
        {
            targetRenderer.material = new Material(targetRenderer.material);
        }
    }
}