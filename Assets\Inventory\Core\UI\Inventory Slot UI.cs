using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using System.Collections.Generic;
using System.Linq;

public class InventorySlotUI : MonoBehaviour, IInventorySlotUI, IPointerEnterHandler, IPointerExit<PERSON><PERSON>ler, IPointerClickHandler
{
    [Header("UI Components")]
    [SerializeField] RectTransform spawnItemParent;
    [SerializeField] GameObject itemModel;
    [SerializeField] public ItemSO itemSO;
    public IInventoryUI parentUI;

    [Tooltip("Image component to display the item's icon.")]
    [SerializeField] private Image itemIcon;

    [Tooltip("Text component to display the item's stack amount.")]
    [SerializeField] private TextMeshProUGUI amountText;

    enum ShowType {Model, Icon};
    [SerializeField] private ShowType showType = ShowType.Model;
    
    public System.Action<IInventorySlotUI, bool> OnSlotHovered { get; set; }
    public System.Action<IInventorySlotUI> OnSlotPressed { get; set; }

    public IInventoryUI GetParentUI() { return parentUI; }


    // Material Area
    private Material _originalMaterial;
    [SerializeField] private Material _maskedMaterial;

    [SerializeField] private RectMask3DController rectMaskController;

    public void Awake()
    {
        GridLayoutGroup layoutGroup = GetComponentInParent<GridLayoutGroup>();
        if (layoutGroup != null)
        {
            Debug.Log("layout encontrado");
            layoutGroup.OnLayoutUpdate += UpdateModelPosition;
        }

        if (parentUI != null && rectMaskController.maskingRect == null) rectMaskController.maskingRect = parentUI.GetViewPort();
    }
    
    /// <summary>
    /// Updates the slot's visual representation based on the slot data.
    /// </summary>
    /// <param name="slotData">The data from the inventory slot to display.</param>
    public void UpdateSlot(IInventorySlot slotData)
    {
        if (parentUI != null && rectMaskController.maskingRect == null) if (parentUI is InventoryUI inventoryUI) rectMaskController.maskingRect = inventoryUI.viewPort;

        if (slotData != null && !slotData.IsEmpty())
        {
            itemSO = slotData.Item();
            switch (showType)
            {
                case ShowType.Model:
                    itemIcon.enabled = false;
                    ShowModel();
                    break;
                case ShowType.Icon:
                    itemIcon.enabled = true;
                    ShowIcon();
                    break;
            }

            // Mostra a quantidade apenas se o item for empilhável e tiver mais de 1
            if (slotData.Amount() > 1)
            {
                amountText.enabled = true;
                amountText.text = slotData.Amount().ToString();
            }
            else
            {
                amountText.enabled = false;
            }
        }
        else
        {
            // Se o slot estiver vazio, limpa a UI
            Clear();
        }
    }

    public void UpdateModelPosition()
    {
        if (showType == ShowType.Model)
        {
            if(itemModel == null) return;
            Debug.Log("Update position");
            RectTransformUtils.FitGameObject(spawnItemParent, itemModel, itemSO.modelInfo);
        }
    }

    [ContextMenu("Fit")]
    public void FitButton()
    {
        if (showType == ShowType.Model)
        {
            if(itemModel == null) return;
            Debug.Log("Update position");
            RectTransformUtils.FitGameObject(spawnItemParent, itemModel, itemSO.modelInfo);
        }
    }

    public void HoverSlot()
    {
        
    }

    public void ShowIcon()
    {
        // Ativa os componentes e define os valores
        itemIcon.enabled = true;
        itemIcon.sprite = itemSO.icon;
    }

    public void ShowModel()
    {
        // Instantiate the item's prefab
        itemModel = Instantiate(itemSO.prefab, spawnItemParent);

        // Get the renderer component
        Renderer itemRenderer = itemModel.GetComponentInChildren<Renderer>();
        
        if (itemRenderer != null)
        {
            // Get the original material. Note: This creates a new instance of the material.
            _originalMaterial = itemRenderer.material;

            // Create a new material using the RectMask3D shader
            Material maskedMaterial = new Material(_maskedMaterial);
            // Apply the main texture from the original material to the new masked material
            maskedMaterial.mainTexture = _originalMaterial.mainTexture;
            
            // Assign the new material to the renderer
            itemRenderer.material = maskedMaterial;

            rectMaskController.SetTarget(itemRenderer);
        }

        // Handle Rigidbody and Collider as before
        Rigidbody rb = itemModel.GetComponentInChildren<Rigidbody>();
        Collider col = itemModel.GetComponentInChildren<Collider>();

        if (rb != null) rb.isKinematic = true;
        if (col != null) col.enabled = false;
    }

    public void OnPointerEnter(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: " + this);
        OnSlotHovered.Invoke(this, true);
    }

    public void OnPointerExit(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: 2 " + this);
        OnSlotHovered.Invoke(this, false);
    }

    public void OnPointerClick(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        if (OnSlotPressed != null) OnSlotPressed(this);
    }

    /// <summary>
    /// Clears the slot's visual representation, making it appear empty.
    /// </summary>
    public void Clear()
    {
        itemSO = null;
        itemIcon.enabled = false;
        itemIcon.sprite = null;
        amountText.enabled = false;
        amountText.text = "";

        // If a model exists, destroy it and its associated material.
        if (itemModel != null)
        {
            Destroy(itemModel);
            itemModel = null;
        }
    }
}